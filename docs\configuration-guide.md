# 配置指南

## 概述

本项目使用环境变量进行配置，所有超时相关的配置都统一使用毫秒（MS）为单位，确保配置的一致性。

## 数据库配置

### 基本连接配置

```env
DB_CONNECTION=sqlsrv
DB_HOST=127.0.0.1
DB_PORT=1433
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 连接池配置

```env
# 连接池最大连接数
DB_POOL_MAX_SIZE=10

# 连接池最小空闲连接数
DB_POOL_MIN_IDLE=2

# 连接超时（毫秒）
DB_POOL_TIMEOUT=5000

# 连接池预热数量
DB_POOL_WARMUP=5

# 连接池监控间隔（毫秒）
DB_POOL_MONITOR_INTERVAL=30000

# 连接池健康检查超时（毫秒）
DB_POOL_HEALTH_CHECK_TIMEOUT=3000
```

## Socket 配置（按需启用）

### 启用/禁用配置

```env
# 设置为 true 启用Socket连接池功能，false 则禁用
SOCKET_ENABLED=false
```

### 基本连接配置

```env
SOCKET_HOST=127.0.0.1
SOCKET_PORT=8080
```

### 超时配置（毫秒）

```env
# 连接超时
SOCKET_CONNECT_TIMEOUT=5000

# 读取超时
SOCKET_READ_TIMEOUT=10000

# 写入超时
SOCKET_WRITE_TIMEOUT=5000

# 总操作超时
SOCKET_OPERATION_TIMEOUT=30000
```

### 重连配置

```env
# 重连间隔（毫秒）
SOCKET_RECONNECT_INTERVAL=3000

# 最大重连次数
SOCKET_MAX_RECONNECT_ATTEMPTS=10

# 心跳间隔（毫秒）
SOCKET_HEARTBEAT_INTERVAL=30000
```

### 缓冲区配置

```env
# 读取缓冲区大小（字节）
SOCKET_READ_BUFFER_SIZE=8192

# 写入缓冲区大小（字节）
SOCKET_WRITE_BUFFER_SIZE=8192
```

## 配置统一性

### 时间单位统一

- **数据库配置**：所有超时配置使用毫秒（MS）
- **Socket 配置**：所有超时配置使用毫秒（MS）
- **环境变量命名**：超时相关配置统一使用 `_MS` 后缀（数据库）或无后缀但明确为毫秒（Socket）

### 配置验证

应用启动时会验证配置的有效性：

1. **数据库连接**：验证连接字符串和连接池参数
2. **Socket 功能**：只有在 `SOCKET_ENABLED=true` 时才会初始化
3. **超时设置**：确保所有超时值都是合理的正数

## 性能调优建议

### 快速网络环境

```env
# 数据库
DB_POOL_TIMEOUT=3000
DB_POOL_HEALTH_CHECK_TIMEOUT=1000

# Socket
SOCKET_CONNECT_TIMEOUT=1000
SOCKET_READ_TIMEOUT=2000
SOCKET_WRITE_TIMEOUT=1000
SOCKET_OPERATION_TIMEOUT=5000
```

### 慢速网络环境

```env
# 数据库
DB_POOL_TIMEOUT=10000
DB_POOL_HEALTH_CHECK_TIMEOUT=5000

# Socket
SOCKET_CONNECT_TIMEOUT=10000
SOCKET_READ_TIMEOUT=30000
SOCKET_WRITE_TIMEOUT=10000
SOCKET_OPERATION_TIMEOUT=60000
```
