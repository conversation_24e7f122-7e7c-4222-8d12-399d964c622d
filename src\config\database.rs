use serde::Deserialize;
use std::env;

/// 数据库配置结构体
#[derive(Debu<PERSON>, <PERSON><PERSON>, Deserialize)]
pub struct DatabaseConfig {
    /// 数据库连接类型
    pub connection: String,
    /// 数据库主机地址
    pub host: String,
    /// 数据库端口
    pub port: u16,
    /// 数据库名称
    pub database: String,
    /// 数据库用户名
    pub username: String,
    /// 数据库密码
    pub password: String,
    /// 连接池最大连接数
    pub pool_max_size: u32,
    /// 连接池最小空闲连接数
    pub pool_min_idle: u32,
    /// 连接超时时间（毫秒）
    pub pool_timeout_ms: u64,
    /// 连接池预热数量
    pub pool_warmup: u32,
    /// 连接池监控间隔（毫秒）
    pub pool_monitor_interval_ms: u64,
    /// 连接池健康检查超时（毫秒）
    pub pool_health_check_timeout_ms: u64,
}

impl DatabaseConfig {
    /// 从环境变量加载数据库配置
    pub fn from_env() -> Self {
        Self {
            connection: env::var("DB_CONNECTION").unwrap_or_else(|_| "sqlsrv".to_string()),
            host: env::var("DB_HOST").unwrap_or_else(|_| "127.0.0.1".to_string()),
            port: env::var("DB_PORT")
                .unwrap_or_else(|_| "1433".to_string())
                .parse()
                .unwrap_or(1433),
            database: env::var("DB_DATABASE").unwrap_or_else(|_| "rustravel".to_string()),
            username: env::var("DB_USERNAME").unwrap_or_else(|_| "sa".to_string()),
            password: env::var("DB_PASSWORD").unwrap_or_else(|_| "".to_string()),
            pool_max_size: env::var("DB_POOL_MAX_SIZE")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .unwrap_or(30),
            pool_min_idle: env::var("DB_POOL_MIN_IDLE")
                .unwrap_or_else(|_| "10".to_string())
                .parse()
                .unwrap_or(10),
            pool_timeout_ms: env::var("DB_POOL_TIMEOUT")
                .unwrap_or_else(|_| "5000".to_string())
                .parse()
                .unwrap_or(5000),
            pool_warmup: env::var("DB_POOL_WARMUP")
                .unwrap_or_else(|_| "5".to_string())
                .parse()
                .unwrap_or(5),
            pool_monitor_interval_ms: env::var("DB_POOL_MONITOR_INTERVAL")
                .unwrap_or_else(|_| "30000".to_string())
                .parse()
                .unwrap_or(30000),
            pool_health_check_timeout_ms: env::var("DB_POOL_HEALTH_CHECK_TIMEOUT")
                .unwrap_or_else(|_| "3000".to_string())
                .parse()
                .unwrap_or(3000),
        }
    }

    /// 获取数据库连接字符串
    pub fn connection_string(&self) -> String {
        format!(
            "jdbc:sqlserver://**:**;databaseName=**;user=**;password=**;encrypt=true;trustServerCertificate=true;integratedSecurity=false;columnEncryptionSetting=Enabled;multiSubnetFailover=true;applicationIntent=ReadWrite;authentication=SqlPassword",
            self.host, self.port, self.database, self.username, self.password
        )
    }
}
