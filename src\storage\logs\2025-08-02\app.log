2025-08-02 09:41:15.193  INFO 开始初始化数据库连接池...
2025-08-02 09:41:15.193  INFO 使用数据库连接类型: sqlsrv
2025-08-02 09:41:15.198  INFO 数据库连接字符串: server=127.0.0.1:1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-08-02 09:41:15.198  INFO 数据库连接池创建成功，最大连接数: 10, 最小空闲连接数: 2, 连接超时: 30秒
2025-08-02 09:41:15.199  INFO 数据库连接池初始化成功
2025-08-02 09:41:15.199  INFO 开始预热数据库连接池，预热数量: 5
2025-08-02 09:41:15.210 ERROR 连接池预热 #4 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:41:15.210 ERROR 连接池预热 #3 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:41:15.210 ERROR 连接池预热 #5 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:41:15.210 ERROR 连接池预热 #1 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:41:15.210 ERROR 连接池预热 #2 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:41:15.218  INFO 连接池预热完成，成功: 5/5
2025-08-02 09:41:15.218  INFO 启动数据库连接池监控，检查间隔: 30秒，超时: 3秒
2025-08-02 09:41:15.218  INFO 数据库服务提供者启动完成
2025-08-02 09:41:15.219  INFO Socket连接字符串: 127.0.0.1:8080
2025-08-02 09:41:15.226 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:41:17.904  INFO 开始初始化数据库连接池...
2025-08-02 09:41:17.905  INFO 使用数据库连接类型: sqlsrv
2025-08-02 09:41:17.905  INFO 数据库连接字符串: server=127.0.0.1:1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-08-02 09:41:17.906  INFO 数据库连接池创建成功，最大连接数: 10, 最小空闲连接数: 2, 连接超时: 30秒
2025-08-02 09:41:17.906  INFO 数据库连接池初始化成功
2025-08-02 09:41:17.911  INFO 开始预热数据库连接池，预热数量: 5
2025-08-02 09:41:17.921 ERROR 连接池预热 #2 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:41:17.922 ERROR 连接池预热 #3 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:41:17.922 ERROR 连接池预热 #1 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:41:17.922 ERROR 连接池预热 #4 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:41:17.922 ERROR 连接池预热 #5 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:41:17.931  INFO 连接池预热完成，成功: 5/5
2025-08-02 09:41:17.931  INFO 启动数据库连接池监控，检查间隔: 30秒，超时: 3秒
2025-08-02 09:41:17.932  INFO 数据库服务提供者启动完成
2025-08-02 09:41:17.932  INFO Socket连接字符串: 127.0.0.1:8080
2025-08-02 09:41:17.939 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:41:25.218 ERROR 创建Socket连接池失败: 连接池错误: 由于目标计算机积极拒绝，无法连接。 (os error 10061)
2025-08-02 09:41:47.939 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:58:39.733  INFO 开始初始化数据库连接池...
2025-08-02 09:58:39.734  INFO 使用数据库连接类型: sqlsrv
2025-08-02 09:58:39.734  INFO 数据库连接字符串: server=127.0.0.1:1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-08-02 09:58:39.734  INFO 数据库连接池创建成功，最大连接数: 10, 最小空闲连接数: 2, 连接超时: 30秒
2025-08-02 09:58:39.735  INFO 数据库连接池初始化成功
2025-08-02 09:58:39.735  INFO 开始预热数据库连接池，预热数量: 5
2025-08-02 09:58:39.746 ERROR 连接池预热 #4 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:58:39.746 ERROR 连接池预热 #5 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:58:39.746 ERROR 连接池预热 #2 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:58:39.746 ERROR 连接池预热 #1 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:58:39.746 ERROR 连接池预热 #3 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:58:39.759  INFO 连接池预热完成，成功: 5/5
2025-08-02 09:58:39.759  INFO 启动数据库连接池监控，检查间隔: 30秒，超时: 3秒
2025-08-02 09:58:39.760  INFO 数据库服务提供者启动完成
2025-08-02 09:58:39.764 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:58:39.766  INFO 服务器运行在 0.0.0.0:3000
2025-08-02 09:59:09.765 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 09:59:39.764 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:00:09.769 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:00:39.763 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:01:09.772 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:01:39.769 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:02:09.775 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:02:15.659  INFO 开始初始化数据库连接池...
2025-08-02 10:02:15.660  INFO 使用数据库连接类型: sqlsrv
2025-08-02 10:02:15.661  INFO 数据库连接字符串: server=127.0.0.1:1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-08-02 10:02:15.661  INFO 数据库连接池创建成功，最大连接数: 10, 最小空闲连接数: 2, 连接超时: 30秒
2025-08-02 10:02:15.662  INFO 数据库连接池初始化成功
2025-08-02 10:02:15.662  INFO 开始预热数据库连接池，预热数量: 5
2025-08-02 10:02:15.672 ERROR 连接池预热 #2 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:02:15.672 ERROR 连接池预热 #1 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:02:15.672 ERROR 连接池预热 #3 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:02:15.672 ERROR 连接池预热 #5 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:02:15.672 ERROR 连接池预热 #4 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:02:15.674  INFO 连接池预热完成，成功: 5/5
2025-08-02 10:02:15.674  INFO 启动数据库连接池监控，检查间隔: 30秒，超时: 3秒
2025-08-02 10:02:15.674  INFO 数据库服务提供者启动完成
2025-08-02 10:02:15.691 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:02:15.694  INFO 服务器运行在 0.0.0.0:3000
2025-08-02 10:02:45.678 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:03:15.679 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:03:45.681 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:04:24.847  INFO 开始初始化数据库连接池...
2025-08-02 10:04:24.847  INFO 使用数据库连接类型: sqlsrv
2025-08-02 10:04:24.849  INFO 数据库连接字符串: server=127.0.0.1:1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-08-02 10:04:24.850  INFO 数据库连接池创建成功，最大连接数: 10, 最小空闲连接数: 2, 连接超时: 30秒
2025-08-02 10:04:24.850  INFO 数据库连接池初始化成功
2025-08-02 10:04:24.850  INFO 开始预热数据库连接池，预热数量: 5
2025-08-02 10:04:24.864 ERROR 连接池预热 #4 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:04:24.864 ERROR 连接池预热 #1 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:04:24.864 ERROR 连接池预热 #5 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:04:24.864 ERROR 连接池预热 #2 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:04:24.864 ERROR 连接池预热 #3 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:04:24.866  INFO 连接池预热完成，成功: 0/5
2025-08-02 10:04:24.866  WARN 连接池预热失败: 所有连接池预热都失败了
2025-08-02 10:04:24.866  INFO 启动数据库连接池监控，检查间隔: 30秒，超时: 3秒
2025-08-02 10:04:24.867  INFO 数据库服务提供者启动完成
2025-08-02 10:04:24.882 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:04:24.901  INFO 服务器运行在 0.0.0.0:3000
2025-08-02 10:04:54.880 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:05:24.872 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:05:54.879 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:06:24.870 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:06:54.881 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:07:24.884 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:07:54.872 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:08:24.877 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:08:54.879 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:09:24.875 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:09:54.873 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:10:24.883 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:10:54.879 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:11:24.875 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:11:54.872 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:12:24.367  INFO 开始初始化数据库连接池...
2025-08-02 10:12:24.367  INFO 使用数据库连接类型: sqlsrv
2025-08-02 10:12:24.368  INFO 数据库连接字符串: server=127.0.0.1:1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-08-02 10:12:24.369  INFO 数据库连接池创建成功，最大连接数: 10, 最小空闲连接数: 2, 连接超时: 30秒
2025-08-02 10:12:24.369  INFO 数据库连接池初始化成功
2025-08-02 10:12:24.370  INFO 开始预热数据库连接池，预热数量: 5
2025-08-02 10:12:24.381 ERROR 连接池预热 #1 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:12:24.381 ERROR 连接池预热 #5 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:12:24.381 ERROR 连接池预热 #3 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:12:24.381 ERROR 连接池预热 #2 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:12:24.381 ERROR 连接池预热 #4 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:12:24.383  INFO 连接池预热完成，成功: 0/5
2025-08-02 10:12:24.383  WARN 连接池预热失败: 所有连接池预热都失败了
2025-08-02 10:12:24.383  INFO 启动数据库连接池监控，检查间隔: 30秒，超时: 3秒
2025-08-02 10:12:24.384  INFO 数据库服务提供者启动完成
2025-08-02 10:12:24.388  INFO 服务器运行在 0.0.0.0:3000
2025-08-02 10:12:24.390 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:12:54.392 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:13:24.387 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:13:54.399 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:14:24.386 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:17:23.208  INFO 开始初始化数据库连接池...
2025-08-02 10:17:23.209  INFO 使用数据库连接类型: sqlsrv
2025-08-02 10:17:23.211  INFO 数据库连接字符串: server=127.0.0.1:1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-08-02 10:17:23.212  INFO 数据库连接池创建成功，最大连接数: 10, 最小空闲连接数: 2, 连接超时: 5000毫秒
2025-08-02 10:17:23.212  INFO 数据库连接池初始化成功
2025-08-02 10:17:23.213  INFO 开始预热数据库连接池，预热数量: 5
2025-08-02 10:17:23.225 ERROR 连接池预热 #4 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:17:23.225 ERROR 连接池预热 #2 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:17:23.225 ERROR 连接池预热 #3 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:17:23.225 ERROR 连接池预热 #1 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:17:23.225 ERROR 连接池预热 #5 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:17:23.227  INFO 连接池预热完成，成功: 0/5
2025-08-02 10:17:23.228  WARN 连接池预热失败: 所有连接池预热都失败了
2025-08-02 10:17:23.228  INFO 启动数据库连接池监控，检查间隔: 30000毫秒，超时: 3000毫秒
2025-08-02 10:17:23.229  INFO 数据库服务提供者启动完成
2025-08-02 10:17:23.233 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:17:23.235  INFO 服务器运行在 0.0.0.0:3000
2025-08-02 10:17:39.617  INFO 开始初始化数据库连接池...
2025-08-02 10:17:39.619  INFO 使用数据库连接类型: sqlsrv
2025-08-02 10:17:39.620  INFO 数据库连接字符串: server=127.0.0.1:1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-08-02 10:17:39.621  INFO 数据库连接池创建成功，最大连接数: 10, 最小空闲连接数: 2, 连接超时: 5000毫秒
2025-08-02 10:17:39.622  INFO 数据库连接池初始化成功
2025-08-02 10:17:39.622  INFO 开始预热数据库连接池，预热数量: 5
2025-08-02 10:17:39.634 ERROR 连接池预热 #2 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:17:39.635 ERROR 连接池预热 #1 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:17:39.635 ERROR 连接池预热 #3 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:17:39.635 ERROR 连接池预热 #5 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:17:39.635 ERROR 连接池预热 #4 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:17:39.637  INFO 连接池预热完成，成功: 0/5
2025-08-02 10:17:39.638  WARN 连接池预热失败: 所有连接池预热都失败了
2025-08-02 10:17:39.638  INFO 启动数据库连接池监控，检查间隔: 30000毫秒，超时: 3000毫秒
2025-08-02 10:17:39.638  INFO 数据库服务提供者启动完成
2025-08-02 10:17:39.642 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:17:39.645  INFO 服务器运行在 0.0.0.0:3000
2025-08-02 10:18:09.653 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:18:39.656 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:19:09.642 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:19:39.643 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:20:09.651 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:20:42.400  INFO 开始初始化数据库连接池...
2025-08-02 10:20:42.401  INFO 使用数据库连接类型: sqlsrv
2025-08-02 10:20:42.402  INFO 数据库连接字符串: server=127.0.0.1:1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-08-02 10:20:42.403  INFO 数据库连接池创建成功，最大连接数: 10, 最小空闲连接数: 2, 连接超时: 5000毫秒
2025-08-02 10:20:42.403  INFO 数据库连接池初始化成功
2025-08-02 10:20:42.404  INFO 开始预热数据库连接池，预热数量: 5
2025-08-02 10:20:42.417 ERROR 连接池预热 #2 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:20:42.417 ERROR 连接池预热 #5 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:20:42.417 ERROR 连接池预热 #1 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:20:42.417 ERROR 连接池预热 #4 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:20:42.417 ERROR 连接池预热 #3 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:20:42.421  INFO 连接池预热完成，成功: 0/5
2025-08-02 10:20:42.422  WARN 连接池预热失败: 所有连接池预热都失败了
2025-08-02 10:20:42.422  INFO 启动数据库连接池监控，检查间隔: 30000毫秒，超时: 3000毫秒
2025-08-02 10:20:42.423  INFO 数据库服务提供者启动完成
2025-08-02 10:20:42.428  INFO 服务器运行在 0.0.0.0:3000
2025-08-02 10:20:42.430 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:21:12.430 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:21:42.433 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:22:43.838  INFO 开始初始化数据库连接池...
2025-08-02 10:22:43.839  INFO 使用数据库连接类型: sqlsrv
2025-08-02 10:22:43.841  INFO 数据库连接字符串: server=127.0.0.1:1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-08-02 10:22:43.842  INFO 数据库连接池创建成功，最大连接数: 10, 最小空闲连接数: 2, 连接超时: 5000毫秒
2025-08-02 10:22:43.842  INFO 数据库连接池初始化成功
2025-08-02 10:22:43.843  INFO 开始预热数据库连接池，预热数量: 5
2025-08-02 10:22:43.857 ERROR 连接池预热 #3 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:22:43.857 ERROR 连接池预热 #2 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:22:43.857 ERROR 连接池预热 #5 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:22:43.857 ERROR 连接池预热 #1 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:22:43.857 ERROR 连接池预热 #4 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:22:43.862  INFO 连接池预热完成，成功: 0/5
2025-08-02 10:22:43.862  WARN 连接池预热失败: 所有连接池预热都失败了
2025-08-02 10:22:43.863  INFO 启动数据库连接池监控，检查间隔: 30000毫秒，超时: 3000毫秒
2025-08-02 10:22:43.863  INFO 数据库服务提供者启动完成
2025-08-02 10:22:43.864 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:22:43.870  INFO 服务器运行在 0.0.0.0:3000
2025-08-02 10:23:13.870 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:23:43.868 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:24:13.871 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:24:43.869 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:25:13.878 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-02 10:25:43.870 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
