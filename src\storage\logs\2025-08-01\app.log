2025-08-01 18:45:32.660  INFO 开始初始化数据库连接池...
2025-08-01 18:45:32.660  INFO 使用数据库连接类型: sqlsrv
2025-08-01 18:45:32.663  INFO 数据库连接字符串: server=127.0.0.1:1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-08-01 18:45:32.664  INFO 数据库连接池创建成功，最大连接数: 10, 最小空闲连接数: 2, 连接超时: 30秒
2025-08-01 18:45:32.665  INFO 数据库连接池初始化成功
2025-08-01 18:45:32.665  INFO 开始预热数据库连接池，预热数量: 5
2025-08-01 18:45:32.676 ERROR 连接池预热 #5 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:45:32.676 ERROR 连接池预热 #3 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:45:32.676 ERROR 连接池预热 #2 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:45:32.676 ERROR 连接池预热 #4 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:45:32.676 ERROR 连接池预热 #1 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:45:32.695  INFO 连接池预热完成，成功: 5/5
2025-08-01 18:45:32.696  INFO 启动数据库连接池监控，检查间隔: 30秒，超时: 3秒
2025-08-01 18:45:32.696  INFO 数据库服务提供者启动完成
2025-08-01 18:45:32.696  INFO Socket连接字符串: 127.0.0.1:8080
2025-08-01 18:45:32.710 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:45:35.430  INFO 开始初始化数据库连接池...
2025-08-01 18:45:35.430  INFO 使用数据库连接类型: sqlsrv
2025-08-01 18:45:35.432  INFO 数据库连接字符串: server=127.0.0.1:1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-08-01 18:45:35.433  INFO 数据库连接池创建成功，最大连接数: 10, 最小空闲连接数: 2, 连接超时: 30秒
2025-08-01 18:45:35.433  INFO 数据库连接池初始化成功
2025-08-01 18:45:35.434  INFO 开始预热数据库连接池，预热数量: 5
2025-08-01 18:45:35.444 ERROR 连接池预热 #5 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:45:35.444 ERROR 连接池预热 #3 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:45:35.444 ERROR 连接池预热 #2 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:45:35.444 ERROR 连接池预热 #1 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:45:35.444 ERROR 连接池预热 #4 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:45:35.450  INFO 连接池预热完成，成功: 5/5
2025-08-01 18:45:35.450  INFO 启动数据库连接池监控，检查间隔: 30秒，超时: 3秒
2025-08-01 18:45:35.451  INFO 数据库服务提供者启动完成
2025-08-01 18:45:35.451  INFO Socket连接字符串: 127.0.0.1:8080
2025-08-01 18:45:35.458 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:45:42.730 ERROR 创建Socket连接池失败: 连接池错误: 由于目标计算机积极拒绝，无法连接。 (os error 10061)
2025-08-01 18:45:42.730 ERROR 请检查以下内容:
2025-08-01 18:45:42.748 ERROR 1. 连接池参数是否合理
2025-08-01 18:45:42.749 ERROR 2. Socket服务器是否可达
2025-08-01 18:45:42.750 ERROR 3. 网络连接是否稳定
2025-08-01 18:56:14.444  INFO 开始初始化数据库连接池...
2025-08-01 18:56:14.444  INFO 使用数据库连接类型: sqlsrv
2025-08-01 18:56:14.445  INFO 数据库连接字符串: server=127.0.0.1:1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-08-01 18:56:14.446  INFO 数据库连接池创建成功，最大连接数: 10, 最小空闲连接数: 2, 连接超时: 30秒
2025-08-01 18:56:14.446  INFO 数据库连接池初始化成功
2025-08-01 18:56:14.447  INFO 开始预热数据库连接池，预热数量: 5
2025-08-01 18:56:14.459 ERROR 连接池预热 #1 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:56:14.461 ERROR 连接池预热 #5 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:56:14.461 ERROR 连接池预热 #3 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:56:14.461 ERROR 连接池预热 #4 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:56:14.461 ERROR 连接池预热 #2 获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:56:14.462  INFO 连接池预热完成，成功: 5/5
2025-08-01 18:56:14.462  INFO 启动数据库连接池监控，检查间隔: 30秒，超时: 3秒
2025-08-01 18:56:14.463  INFO 数据库服务提供者启动完成
2025-08-01 18:56:14.463  INFO Socket连接字符串: 127.0.0.1:8080
2025-08-01 18:56:14.467 ERROR 数据库连接池健康检查获取连接失败: Error occurred while creating a new object: An error occured during the attempt of performing I/O: 不知道这样的主机。 (os error 11001)
2025-08-01 18:56:21.752 ERROR 创建Socket连接池失败: 连接池错误: 由于目标计算机积极拒绝，无法连接。 (os error 10061)
2025-08-01 18:56:21.753 ERROR 请检查以下内容:
2025-08-01 18:56:21.755 ERROR 1. 连接池参数是否合理
2025-08-01 18:56:21.756 ERROR 2. Socket服务器是否可达
2025-08-01 18:56:21.757 ERROR 3. 网络连接是否稳定
