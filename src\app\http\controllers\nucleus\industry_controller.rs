use crate::app::models::transit::code::Code;
use crate::app::providers::DatabaseServiceProvider;
use crate::app::services::nucleus::industry_service::IndustryService;
use crate::app::services::pivotal::print_service::PrintService;
use crate::app::traits::response::track;
use axum::extract::{Json, State};
use axum::response::IntoResponse;
use futures_util::TryStreamExt;
use reqwest::Client;
use serde::Deserialize;
use serde_json::{json, Value};
use std::sync::Arc;
use tracing::{error, info};

/// 取样参数
#[derive(Debug, Deserialize)]
pub struct SamplingParams {
    /// 操作类型
    #[serde(rename = "operationType")]
    pub operation_type: String,
    /// 序列码
    pub code: String,
}

/// 拆箱参数
#[derive(Debug, Deserialize)]
pub struct UnpackParams {
    /// 箱码
    case: String,
    /// 瓶码列表
    codes: Vec<String>,
}

/// 并箱参数
#[derive(Debug, Deserialize)]
pub struct MergeParams {
    /// 瓶码列表
    codes: Vec<String>,
}

/// 解绑参数
#[derive(Debug, Deserialize)]
pub struct UnbindParams {
    /// 序列码
    pub code: String,
}

/// 聚合参数
#[derive(Debug, Deserialize)]
pub struct AggregateParams {
    /// 聚合方式：newPackage(新建包装) 或 putInto(放入)
    #[serde(rename = "aggregateType")]
    pub aggregate_type: String,
    /// 托盘码（放入模式必填）
    #[serde(rename = "palletCode")]
    pub pallet_code: Option<String>,
    /// 箱码列表
    #[serde(rename = "boxCodes")]
    pub box_codes: Vec<String>,
}

/// 取出参数
#[derive(Debug, Deserialize)]
pub struct ExtractParams {
    /// 序列码
    pub code: String,
}

/// 替换参数
#[derive(Debug, Deserialize)]
pub struct ReplaceParams {
    /// 原始序列码
    #[serde(rename = "originCode")]
    pub origin_code: String,
    /// 替换序列码
    #[serde(rename = "replaceCode")]
    pub replace_code: String,
}

/// 重打印参数
#[derive(Debug, Deserialize)]
pub struct ReprintParams {
    /// 序列码
    pub code: String,
}

/// PDA核心业务处理控制器
pub struct IndustryController;

impl IndustryController {
    /// 取样或报废
    pub async fn sampling(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Json(params): Json<SamplingParams>,
    ) -> impl IntoResponse {
        info!("[取样★报废] 开始取样★报废操作流程");
        info!(
            "[取样★报废] 接收到请求参数: operation_type={}, code={}",
            params.operation_type, params.code
        );

        // 验证操作类型
        if params.operation_type.is_empty() {
            error!("[取样★报废] 参数错误: 缺少操作类型");
            return track().defeat().message("请传入操作类型").build();
        }

        // 验证序列码
        if params.code.is_empty() {
            error!("[取样★报废] 参数错误: 缺少序列码");
            return track().defeat().message("请传入序列码").build();
        }

        // 获取操作类型名称和状态码
        let (operation_name, status) = match params.operation_type.as_str() {
            "sampling" => ("取样", 3),
            "scrap" => ("报废", 4),
            _ => {
                error!("[取样★报废] 无效的操作类型: {}", params.operation_type);
                return track()
                    .defeat()
                    .message("无效的操作类型，只支持取样或报废")
                    .build();
            }
        };

        info!("[取样★报废] 操作类型名称: {}", operation_name);

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[取样★报废] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[取样★报废] 数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        info!("[取样★报废] 开始查询序列码: {}", params.code);

        // 查询序列码状态的SQL
        let sql = r#"
            SELECT id, dataID, poNumber, productCode, orderId, batchNo, levelCode, levelName, 
                   resCode, snCode, code, amount, parentCode, boxCode, palletCode, typeFlag, reqID, 
                   codeFlag, boxFlag, estate, CONVERT(varchar(19), createTime, 120) as createTime, 
                   CONVERT(varchar(19), updateTime, 120) as updateTime, fileID, helperCode, zeroBox, 
                   qualityLevel, field1, field2, boxSn
            FROM transit_code WITH (NOLOCK)
            WHERE code = @P1
        "#;

        // 查询序列码信息
        let code_row = match conn.query(sql, &[&params.code]).await {
            Ok(mut stream) => {
                let mut found = false;
                let mut code_result = None;

                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        found = true;
                        // 使用Code的renovation方法处理字段
                        let code = Code::renovation(&row);
                        info!(
                            "[取样★报废] 查询到序列码信息: code_flag={}, level_code={}, zero_box={}, batch_no={}",
                            code.code_flag, code.level_code, code.zero_box, code.batch_no
                        );
                        code_result = Some(code);
                        break;
                    }
                }

                if found {
                    code_result.unwrap()
                } else {
                    error!("[取样★报废] 未查询到序列码信息");
                    return track().defeat().message("未查询到序列码信息").build();
                }
            }
            Err(e) => {
                error!("[取样★报废] 查询序列码信息失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("查询序列码信息失败: {}", e))
                    .build();
            }
        };

        // 判断序列码状态是否为关联状态（codeFlag=2表示关联状态）
        if code_row.code_flag != 2 {
            error!(
                "[取样★报废] 序列码状态异常: code_flag={}",
                code_row.code_flag
            );
            return track()
                .defeat()
                .message("序列码状态异常，不是关联状态")
                .build();
        }

        info!(
            "[取样★报废] 序列码状态正常: code_flag={}",
            code_row.code_flag
        );

        // 开启事务
        if let Err(e) = conn.simple_query("BEGIN TRANSACTION").await {
            error!("[取样★报废] 开启事务失败: {}", e);
            return track()
                .defeat()
                .message(format!("开启事务失败: {}", e))
                .build();
        }

        // 处理结果
        let result = match code_row.level_code {
            // 托盘码处理
            4 => {
                info!("[取样★报废] 开始托盘码{}业务处理", code_row.code);

                // 托盘码处理条件：palletCode等于自身，parentCode和boxCode为空
                let parent_empty = code_row.parent_code.as_ref().map_or(true, |v| v.is_empty());
                let box_empty = code_row.box_code.as_ref().map_or(true, |v| v.is_empty());
                let pallet_is_self = code_row
                    .pallet_code
                    .as_ref()
                    .map_or(false, |v| v == &code_row.code);

                if !parent_empty || !box_empty || !pallet_is_self {
                    error!(
                        "[取样★报废] 托盘码不符合条件: parentCode={}, boxCode={}, palletCode={}",
                        code_row.parent_code.as_ref().unwrap_or(&"NULL".to_string()),
                        code_row.box_code.as_ref().unwrap_or(&"NULL".to_string()),
                        code_row.pallet_code.as_ref().unwrap_or(&"NULL".to_string()),
                    );
                    Err("托盘码不符合条件".to_string())
                } else {
                    // 调用服务层方法处理托盘码
                    IndustryService::process_pallet_sampling_scrap(&mut conn, &code_row, status)
                        .await
                        .map(|_| (String::new(), None)) // 托盘码处理成功后返回空数据
                }
            }
            // 箱码处理
            3 => {
                let parent_code = code_row.parent_code.as_ref();
                let pallet_code = code_row.pallet_code.as_ref();

                // 检查是否同时存在parentCode和palletCode，且它们相等
                let has_pallet = if let (Some(parent), Some(pallet)) = (parent_code, pallet_code) {
                    !parent.is_empty() && !pallet.is_empty() && parent == pallet
                } else {
                    false
                };

                info!("[取样★报废] 箱码关联托盘: {}", has_pallet);

                // 根据是否有托盘调用不同的服务方法
                if has_pallet {
                    let pallet_code = pallet_code.unwrap();
                    IndustryService::process_box_with_pallet_sampling_scrap(
                        &mut conn,
                        &code_row,
                        status,
                        &pallet_code,
                    )
                    .await
                } else {
                    IndustryService::process_box_without_pallet_sampling_scrap(
                        &mut conn, &code_row, status,
                    )
                    .await
                }
            }
            // 瓶码处理
            1 => {
                // 检查瓶码是否关联了托盘码
                let has_pallet = code_row
                    .pallet_code
                    .as_ref()
                    .map_or(false, |p| !p.is_empty());

                // 检查瓶码是否关联了大箱码
                let has_box = code_row.box_code.as_ref().map_or(false, |b| !b.is_empty());
                let has_parent = code_row
                    .parent_code
                    .as_ref()
                    .map_or(false, |p| !p.is_empty());
                let parent_eq_box = if has_parent && has_box {
                    code_row.parent_code.as_ref().unwrap() == code_row.box_code.as_ref().unwrap()
                } else {
                    false
                };

                info!(
                    "[取样★报废] 瓶码关联信息: has_pallet={}, has_box={}, has_parent={}, parent_eq_box={}",
                    has_pallet, has_box, has_parent, parent_eq_box
                );

                // 验证瓶码关联关系是否符合条件
                if has_pallet {
                    // 如果关联了托盘码，必须同时关联大箱，且parentCode=boxCode
                    if !(has_box && has_parent && parent_eq_box) {
                        error!("[取样★报废] 瓶码关联托盘但关联关系异常，不符合取样条件");
                        Err("瓶码关联托盘但关联关系异常，不符合取样条件".to_string())
                    } else {
                        // 调用服务层方法处理瓶码
                        IndustryService::process_bottle_sampling_scrap(&mut conn, &code_row, status)
                            .await
                            .map(|maybe_zero_box| maybe_zero_box)
                    }
                } else {
                    // 如果未关联托盘码，检查是否正常关联大箱或完全无关联
                    if !(has_box && has_parent && parent_eq_box) && !((!has_box) && (!has_parent)) {
                        error!("[取样★报废] 瓶码关联关系异常，不符合取样条件");
                        Err("瓶码关联关系异常，不符合取样条件".to_string())
                    } else {
                        // 调用服务层方法处理瓶码
                        IndustryService::process_bottle_sampling_scrap(&mut conn, &code_row, status)
                            .await
                            .map(|maybe_zero_box| maybe_zero_box)
                    }
                }
            }
            // 其他层级码
            _ => {
                error!("[取样★报废] 不支持的层级码: {}", code_row.level_code);
                Err(format!("不支持的层级码: {}", code_row.level_code))
            }
        };

        // 根据处理结果提交或回滚事务
        match result {
            Ok((pallet_code, maybe_box_code)) => {
                // 提交事务
                let commit_result = conn.simple_query("COMMIT TRANSACTION").await;
                if commit_result.is_ok() {
                    info!("[取样★报废] 事务提交成功，{}操作完成", operation_name);

                    // 根据层级码返回不同的响应数据
                    match code_row.level_code {
                        // 托盘码处理 - 返回空数据
                        4 => track()
                            .victory()
                            .message(format!("序列码{}成功", operation_name))
                            .build(),
                        // 箱码处理 - 返回托盘码
                        3 => track()
                            .victory()
                            .message(format!("序列码{}成功", operation_name))
                            .data(serde_json::json!({
                                "palletCode": pallet_code
                            }))
                            .build(),
                        // 瓶码处理 - 返回托盘码和箱码（零箱码）
                        1 => {
                            let box_code = maybe_box_code.unwrap_or_default();

                            track()
                                .victory()
                                .message(format!("序列码{}成功", operation_name))
                                .data(serde_json::json!({
                                    "palletCode": pallet_code,
                                    "boxCode": box_code
                                }))
                                .build()
                        }
                        // 其他层级码
                        _ => track()
                            .victory()
                            .message(format!("序列码{}成功", operation_name))
                            .build(),
                    }
                } else {
                    let err = commit_result.err().unwrap();
                    error!("[取样★报废] 提交事务失败: {}", err);

                    // 尝试回滚事务
                    let rollback_result = conn.simple_query("ROLLBACK TRANSACTION").await;
                    if rollback_result.is_ok() {
                        info!("[取样★报废] 事务已回滚");
                    } else {
                        error!(
                            "[取样★报废] 回滚事务也失败了: {}",
                            rollback_result.err().unwrap()
                        );
                    }

                    track()
                        .defeat()
                        .message(format!("提交事务失败: {}", err))
                        .build()
                }
            }
            Err(message) => {
                // 回滚事务
                let rollback_result = conn.simple_query("ROLLBACK TRANSACTION").await;
                if rollback_result.is_ok() {
                    info!("[取样★报废] 事务已回滚");
                } else {
                    error!(
                        "[取样★报废] 回滚事务失败: {}",
                        rollback_result.err().unwrap()
                    );
                }

                track()
                    .defeat()
                    .message(format!("序列码{}失败: {}", operation_name, message))
                    .build()
            }
        }
    }

    /// 拆箱
    pub async fn unpack(
        State(_db): State<Arc<DatabaseServiceProvider>>,
        Json(params): Json<UnpackParams>,
    ) -> impl IntoResponse {
        info!("[拆箱] 开始拆箱操作流程");

        info!(
            "[拆箱] 接收到请求参数: case={}, codes={:?}",
            params.case, params.codes
        );

        // 验证参数
        if params.case.is_empty() {
            error!("[拆箱] 参数错误: 缺少箱码");
            return track().defeat().message("请传入箱码").build();
        }

        if params.codes.is_empty() {
            error!("[拆箱] 参数错误: 缺少瓶码列表");
            return track().defeat().message("请传入瓶码列表").build();
        }

        // 创建HTTP客户端
        let client = match Client::builder().build() {
            Ok(client) => client,
            Err(e) => {
                error!("[拆箱] HTTP客户端创建失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("HTTP客户端创建失败: {}", e))
                    .build();
            }
        };

        // 准备请求数据
        let request_data = json!({
            "case": params.case,
            "codes": params.codes
        });

        // 发送HTTP请求
        info!(
            "[拆箱] 发送HTTP请求到拆箱接口: {}",
            request_data.to_string()
        );
        let response = match client
            .post("http://localhost:8000/api/produce/order/split/box")
            .json(&request_data)
            .send()
            .await
        {
            Ok(response) => response,
            Err(e) => {
                error!("[拆箱]HTTP请求失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("HTTP请求失败: {}", e))
                    .build();
            }
        };

        // 解析响应·
        match response.json::<Value>().await {
            Ok(json_response) => {
                // 检查响应状态码
                if let Some(code) = json_response.get("code").and_then(|c| c.as_i64()) {
                    if code == 0 {
                        info!("[拆箱] 拆箱成功，响应数据: {}", json_response.to_string());
                        // 拆箱成功，返回数据
                        track()
                            .victory()
                            .message("拆箱成功")
                            .data(json_response.get("data").cloned().unwrap_or(json!([])))
                            .build()
                    } else {
                        // 获取错误消息
                        let error_message = json_response
                            .get("message")
                            .and_then(|m| m.as_str())
                            .unwrap_or("拆箱失败")
                            .to_string();

                        error!("[拆箱] 拆箱失败: {}", error_message);
                        track().defeat().message(error_message).build()
                    }
                } else {
                    error!("[拆箱] 响应格式错误: 缺少状态码");
                    track().defeat().message("响应格式错误: 缺少状态码").build()
                }
            }
            Err(e) => {
                error!("[拆箱] 解析响应数据失败: {}", e);
                track()
                    .defeat()
                    .message(format!("解析响应数据失败: {}", e))
                    .build()
            }
        }
    }

    /// 并箱
    pub async fn merge(
        State(_db): State<Arc<DatabaseServiceProvider>>,
        Json(params): Json<MergeParams>,
    ) -> impl IntoResponse {
        info!("[并箱] 开始并箱操作流程");
        info!("[并箱] 接收到请求参数: codes={:?}", params.codes);

        // 验证参数
        if params.codes.is_empty() {
            error!("[并箱] 参数错误: 缺少箱码列表");
            return track().defeat().message("请传入箱码列表").build();
        }

        // 创建HTTP客户端
        let client = match Client::builder().build() {
            Ok(client) => client,
            Err(e) => {
                error!("[并箱] HTTP客户端创建失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("HTTP客户端创建失败: {}", e))
                    .build();
            }
        };

        // 准备请求数据
        let request_data = json!({
            "codes": params.codes
        });

        // 发送HTTP请求
        info!(
            "[并箱] 发送HTTP请求到并箱接口: {}",
            request_data.to_string()
        );
        let response = match client
            .post("http://localhost:8000/api/produce/order/merge/box")
            .json(&request_data)
            .send()
            .await
        {
            Ok(response) => response,
            Err(e) => {
                error!("[并箱] HTTP请求失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("HTTP请求失败: {}", e))
                    .build();
            }
        };

        // 解析响应
        match response.json::<Value>().await {
            Ok(json_response) => {
                // 检查响应状态码
                if let Some(code) = json_response.get("code").and_then(|c| c.as_i64()) {
                    if code == 0 {
                        info!("[并箱] 并箱成功");
                        // 并箱成功，返回数据
                        track()
                            .victory()
                            .message("并箱成功")
                            .data(json_response.get("data").cloned().unwrap_or(json!({})))
                            .build()
                    } else {
                        // 获取错误消息
                        let error_message = json_response
                            .get("message")
                            .and_then(|m| m.as_str())
                            .unwrap_or("并箱失败")
                            .to_string();

                        error!("[并箱] 并箱失败: {}", error_message);
                        track().defeat().message(error_message).build()
                    }
                } else {
                    error!("[并箱] 响应格式错误: 缺少状态码");
                    track().defeat().message("响应格式错误: 缺少状态码").build()
                }
            }
            Err(e) => {
                error!("[并箱] 解析响应数据失败: {}", e);
                track()
                    .defeat()
                    .message(format!("解析响应数据失败: {}", e))
                    .build()
            }
        }
    }

    /// 解绑
    pub async fn unbind(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Json(params): Json<UnbindParams>,
    ) -> impl IntoResponse {
        info!("[解绑] 开始解绑操作流程");
        info!("[解绑] 接收到请求参数: code={}", params.code);

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[解绑] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[解绑] 数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        info!("[解绑] 开始查询序列码: {}", params.code);

        // 查询序列码状态的SQL
        let sql = r#"
            SELECT id, dataID, poNumber, productCode, orderId, batchNo, levelCode, levelName, 
                   resCode, snCode, code, amount, parentCode, boxCode, palletCode, typeFlag, reqID, 
                   codeFlag, boxFlag, estate, CONVERT(varchar(19), createTime, 120) as createTime, 
                   CONVERT(varchar(19), updateTime, 120) as updateTime, fileID, helperCode, zeroBox, 
                   qualityLevel, field1, field2, boxSn
            FROM transit_code WITH (NOLOCK)
            WHERE code = @P1
        "#;

        // 查询序列码信息
        let code_row = match conn.query(sql, &[&params.code]).await {
            Ok(mut stream) => {
                let mut found = false;
                let mut code_result = None;

                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        found = true;
                        // 使用Code的renovation方法处理字段
                        let code = Code::renovation(&row);
                        info!(
                            "[解绑] 查询到序列码信息: code_flag={}, level_code={}, zero_box={}, batch_no={}",
                            code.code_flag, code.level_code, code.zero_box, code.batch_no
                        );
                        code_result = Some(code);
                        break;
                    }
                }

                if found {
                    code_result.unwrap()
                } else {
                    error!("[解绑] 未查询到序列码信息");
                    return track().defeat().message("未查询到序列码信息").build();
                }
            }
            Err(e) => {
                error!("[解绑] 查询序列码失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("查询序列码失败: {}", e))
                    .build();
            }
        };

        // 判断序列码状态是否为关联状态（codeFlag=2表示关联状态）
        if code_row.code_flag != 2 {
            error!("[解绑] 序列码状态异常: code_flag={}", code_row.code_flag);
            return track()
                .defeat()
                .message("序列码状态异常，不是关联状态，无法解绑")
                .build();
        }
        info!("[解绑] 序列码状态正常: code_flag={}", code_row.code_flag);

        // 开启事务
        if let Err(e) = conn.simple_query("BEGIN TRANSACTION").await {
            error!("[解绑] 开启事务失败: {}", e);
            return track()
                .defeat()
                .message(format!("开启事务失败: {}", e))
                .build();
        }

        // 处理结果
        let result = match code_row.level_code {
            // 托盘码处理
            4 => {
                info!("[解绑] 开始判断托盘码是否符合解绑条件");

                // 托盘码解绑条件：parentCode和boxCode为空，palletCode等于自身
                let parent_empty = code_row.parent_code.as_ref().map_or(true, |v| v.is_empty());
                let box_empty = code_row.box_code.as_ref().map_or(true, |v| v.is_empty());
                let pallet_is_self = code_row
                    .pallet_code
                    .as_ref()
                    .map_or(false, |v| v == &code_row.code);

                if !parent_empty || !box_empty {
                    error!(
                        "[解绑] 托盘码不符合解绑条件: parentCode={}, boxCode={}",
                        parent_empty, box_empty
                    );
                    Err("托盘码不符合解绑条件".to_string())
                } else if !pallet_is_self {
                    error!(
                        "[解绑] 托盘码不符合解绑条件: palletCode={}, code={}",
                        code_row.pallet_code.as_ref().unwrap(),
                        code_row.code
                    );
                    Err("托盘码不符合解绑条件".to_string())
                } else {
                    // 调用服务层方法解绑托盘码
                    IndustryService::unbind_pallet_associations(
                        &mut conn,
                        &code_row,
                        &code_row.code,
                    )
                    .await
                }
            }
            // 箱码处理
            3 => {
                info!(
                    "[解绑] 开始判断{}是否符合解绑条件",
                    if code_row.zero_box == 1 {
                        "零箱码"
                    } else {
                        "整箱码"
                    }
                );

                // 箱码解绑条件：parentCode和palletCode不为空且相等
                let parent_code = code_row.parent_code.as_ref();
                let pallet_code = code_row.pallet_code.as_ref();

                // 检查是否同时存在parentCode和palletCode，且它们相等
                if let (Some(parent), Some(pallet)) = (parent_code, pallet_code) {
                    if !parent.is_empty() && !pallet.is_empty() && parent == pallet {
                        info!("[解绑] 大箱关联的托盘码: {}", parent);
                        info!("[解绑] 开始解绑托盘码下所有大箱及相关瓶码与托盘的关联关系");

                        // 调用服务层方法解绑箱码与托盘码
                        IndustryService::unbind_pallet_associations(&mut conn, &code_row, parent)
                            .await
                    } else {
                        error!(
                            "[解绑] 大箱不符合解绑条件: parentCode={}, palletCode={}",
                            parent, pallet
                        );
                        Err("大箱不符合解绑条件".to_string())
                    }
                } else {
                    error!(
                        "[解绑] 大箱不符合解绑条件: parentCode={:?}, palletCode={:?}",
                        parent_code, pallet_code
                    );
                    Err("大箱不符合解绑条件".to_string())
                }
            }
            // 其他层级码
            _ => {
                error!("[解绑] 不支持的层级码: {}", code_row.level_code);
                Err(format!("不支持的层级码: {}", code_row.level_code))
            }
        };

        // 根据处理结果提交或回滚事务
        match result {
            Ok(_) => {
                // 提交事务
                let commit_result = conn.simple_query("COMMIT TRANSACTION").await;
                if commit_result.is_ok() {
                    info!("[解绑] 事务提交成功，解绑操作完成");
                    track()
                        .victory()
                        .message("序列码解绑成功")
                        .data(code_row.to_response())
                        .build()
                } else {
                    let err = commit_result.err().unwrap();
                    error!("[解绑] 提交事务失败: {}", err);

                    // 尝试回滚事务
                    let rollback_result = conn.simple_query("ROLLBACK TRANSACTION").await;
                    if rollback_result.is_ok() {
                        info!("[解绑] 事务已回滚");
                    } else {
                        error!(
                            "[解绑] 回滚事务也失败了: {}",
                            rollback_result.err().unwrap()
                        );
                    }

                    track()
                        .defeat()
                        .message(format!("提交事务失败: {}", err))
                        .build()
                }
            }
            Err(message) => {
                // 回滚事务
                let rollback_result = conn.simple_query("ROLLBACK TRANSACTION").await;
                if rollback_result.is_ok() {
                    info!("[解绑] 事务已回滚");
                } else {
                    error!("[解绑] 回滚事务失败: {}", rollback_result.err().unwrap());
                }

                track()
                    .defeat()
                    .message(format!("序列码解绑失败: {}", message))
                    .build()
            }
        }
    }

    /// 聚合
    pub async fn aggregate(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Json(params): Json<AggregateParams>,
    ) -> impl IntoResponse {
        info!("[聚合] 开始聚合操作流程");
        info!(
            "[聚合] 接收到请求参数: aggregate_type={}, box_codes数量={}",
            params.aggregate_type,
            params.box_codes.len()
        );

        // 验证基本参数
        // 验证聚合类型
        if params.aggregate_type.is_empty()
            || (params.aggregate_type != "newPackage" && params.aggregate_type != "putInto")
        {
            error!("[聚合] 无效的聚合模式: {}", params.aggregate_type);
            return track()
                .defeat()
                .message("无效的聚合模式，只支持放入或新建包装")
                .build();
        }

        // 验证箱码列表
        if params.box_codes.is_empty() {
            error!("[聚合] 参数错误: 缺少箱码列表");
            return track().defeat().message("请传入箱码列表").build();
        }

        // 如果是放入模式，验证托盘码
        if params.aggregate_type == "putInto"
            && (params.pallet_code.is_none() || params.pallet_code.as_ref().unwrap().is_empty())
        {
            error!("[聚合] 参数错误: 放入模式缺少托盘码");
            return track().defeat().message("放入模式请传入托盘码").build();
        }

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[聚合] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[聚合] 数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 查询序列码状态的SQL
        let sql = r#"
            SELECT id, dataID, poNumber, productCode, orderId, batchNo, levelCode, levelName, 
                   resCode, snCode, code, amount, parentCode, boxCode, palletCode, typeFlag, reqID, 
                   codeFlag, boxFlag, estate, CONVERT(varchar(19), createTime, 120) as createTime, 
                   CONVERT(varchar(19), updateTime, 120) as updateTime, fileID, helperCode, zeroBox, 
                   qualityLevel, field1, field2, boxSn
            FROM transit_code WITH (NOLOCK)
            WHERE code = @P1
        "#;

        // 开始事务
        if let Err(e) = conn.simple_query("BEGIN TRANSACTION").await {
            error!("[聚合] 开启事务失败: {}", e);
            return track()
                .defeat()
                .message(format!("开启事务失败: {}", e))
                .build();
        }

        // 根据聚合类型执行不同的操作
        let result = match params.aggregate_type.as_str() {
            "putInto" => {
                let pallet_code = params.pallet_code.as_ref().unwrap().clone();
                info!("[聚合] |放入| 托盘码: {}", pallet_code);

                // 1. 验证托盘码
                let pallet_row = match conn.query(sql, &[&pallet_code]).await {
                    Ok(mut stream) => {
                        let mut found = false;
                        let mut code_result = None;

                        while let Ok(Some(item)) = stream.try_next().await {
                            if let tiberius::QueryItem::Row(row) = item {
                                found = true;
                                let code = Code::renovation(&row);
                                info!(
                                    "[聚合] |放入| 查询到托盘码信息: code_flag={}, level_code={}",
                                    code.code_flag, code.level_code
                                );
                                code_result = Some(code);
                                break;
                            }
                        }

                        if found {
                            code_result.unwrap()
                        } else {
                            error!("[聚合] |放入| 未查询到托盘码信息");
                            return track().defeat().message("未查询到托盘码信息").build();
                        }
                    }
                    Err(e) => {
                        error!("[聚合] |放入| 查询托盘码失败: {}", e);
                        return track()
                            .defeat()
                            .message(format!("查询托盘码失败: {}", e))
                            .build();
                    }
                };

                // 验证托盘码条件
                // 验证托盘码状态是否为关联状态
                if pallet_row.code_flag != 2 {
                    error!("[聚合] |放入| 托盘码状态异常: {}", pallet_row.code_flag);
                    return track()
                        .defeat()
                        .message("托盘码状态异常，不是关联状态")
                        .build();
                }

                // 验证托盘码层级是否为4（托盘码）
                if pallet_row.level_code != 4 {
                    error!(
                        "[聚合] |放入| 不是托盘码: level_code={}",
                        pallet_row.level_code
                    );
                    return track()
                        .defeat()
                        .message("不是托盘码，无法执行放入操作")
                        .build();
                }

                // 验证托盘码parentCode和boxCode为空
                let parent_empty = pallet_row
                    .parent_code
                    .as_ref()
                    .map_or(true, |v| v.is_empty());
                let box_empty = pallet_row.box_code.as_ref().map_or(true, |v| v.is_empty());
                let pallet_is_self = pallet_row
                    .pallet_code
                    .as_ref()
                    .map_or(false, |v| v == &pallet_row.code);

                if !parent_empty || !box_empty || !pallet_is_self {
                    error!(
                        "[聚合] |放入| 托盘码不符合条件: parentCode={}, boxCode={}, palletCode={}",
                        pallet_row
                            .parent_code
                            .as_ref()
                            .unwrap_or(&"NULL".to_string()),
                        pallet_row.box_code.as_ref().unwrap_or(&"NULL".to_string()),
                        pallet_row
                            .pallet_code
                            .as_ref()
                            .unwrap_or(&"NULL".to_string()),
                    );
                    return track().defeat().message("托盘码不符合放入条件").build();
                }

                // 验证箱码数量是否超过限制
                let product_code = &pallet_row.product_code;
                if let Err(e) = IndustryService::validate_pallet_box_count(
                    &mut conn,
                    "putInto",
                    Some(&pallet_code),
                    product_code,
                    params.box_codes.len(),
                )
                .await
                {
                    error!("[聚合] |放入| 验证箱数限制失败: {}", e);
                    return track().defeat().message(e).build();
                }

                // 2. 验证并处理箱码列表
                let batch_no = pallet_row.batch_no.clone();
                let mut processed_boxes = Vec::new();

                for (index, box_code) in params.box_codes.iter().enumerate() {
                    info!(
                        "[聚合] |放入| 处理箱码 {}/{}: {}",
                        index + 1,
                        params.box_codes.len(),
                        box_code
                    );

                    // 查询箱码信息
                    let box_row = match conn.query(sql, &[box_code]).await {
                        Ok(mut stream) => {
                            let mut found = false;
                            let mut code_result = None;

                            while let Ok(Some(item)) = stream.try_next().await {
                                if let tiberius::QueryItem::Row(row) = item {
                                    found = true;
                                    let code = Code::renovation(&row);
                                    info!(
                                        "[聚合] |放入| 查询到箱码信息: code_flag={}, level_code={}",
                                        code.code_flag, code.level_code
                                    );
                                    code_result = Some(code);
                                    break;
                                }
                            }

                            if found {
                                code_result.unwrap()
                            } else {
                                error!("[聚合] |放入| 未查询到箱码信息: {}", box_code);
                                return track()
                                    .defeat()
                                    .message(format!("未查询到箱码信息: {}", box_code))
                                    .build();
                            }
                        }
                        Err(e) => {
                            error!("[聚合] |放入| 查询箱码失败: {}", e);
                            return track()
                                .defeat()
                                .message(format!("查询箱码失败: {}", e))
                                .build();
                        }
                    };

                    // 验证箱码条件
                    // 验证箱码状态是否为关联状态
                    if box_row.code_flag != 2 {
                        error!("[聚合] |放入| 箱码状态异常: {}", box_row.code_flag);
                        return track()
                            .defeat()
                            .message(format!("箱码{}状态异常，不是关联状态", box_code))
                            .build();
                    }

                    // 验证箱码层级是否为3（箱码）
                    if box_row.level_code != 3 {
                        error!("[聚合] |放入| 不是箱码: level_code={}", box_row.level_code);
                        return track()
                            .defeat()
                            .message(format!("{}不是箱码，无法执行放入操作", box_code))
                            .build();
                    }

                    // 验证箱码parentCode和palletCode为空
                    let box_parent_empty =
                        box_row.parent_code.as_ref().map_or(true, |v| v.is_empty());
                    let box_pallet_empty =
                        box_row.pallet_code.as_ref().map_or(true, |v| v.is_empty());

                    if !box_parent_empty || !box_pallet_empty {
                        error!(
                            "[聚合] |放入| 箱码不符合条件: parentCode={}, palletCode={}",
                            box_row.parent_code.as_ref().unwrap_or(&"NULL".to_string()),
                            box_row.pallet_code.as_ref().unwrap_or(&"NULL".to_string()),
                        );
                        return track()
                            .defeat()
                            .message(format!("箱码{}不符合放入条件", box_code))
                            .build();
                    }

                    // 验证箱码批次与托盘批次一致
                    if box_row.batch_no != batch_no {
                        error!(
                            "[聚合] |放入| 箱码批次与托盘批次不一致: box_batch={}, pallet_batch={}",
                            box_row.batch_no, batch_no
                        );
                        return track()
                            .defeat()
                            .message(format!("箱码{}批次与托盘批次不一致", box_code))
                            .build();
                    }

                    // 箱码验证通过，添加到处理列表
                    processed_boxes.push(box_row);
                }

                // 3. 调用服务层方法执行放入操作
                match IndustryService::execute_put_into(&mut conn, &pallet_code, &params.box_codes)
                    .await
                {
                    Ok(_) => Ok(pallet_code.clone()),
                    Err(e) => Err(e),
                }
            }
            "newPackage" => {
                info!("[聚合] |新建包装| 开始执行新建包装模式");

                // 1. 验证并处理箱码列表
                let mut processed_boxes = Vec::new();
                let mut batch_no = String::new();
                let mut product_code = String::new();

                for (index, box_code) in params.box_codes.iter().enumerate() {
                    info!(
                        "[聚合] |新建包装| 处理箱码 {}/{}: {}",
                        index + 1,
                        params.box_codes.len(),
                        box_code
                    );

                    // 查询箱码信息
                    let box_row = match conn.query(sql, &[box_code]).await {
                        Ok(mut stream) => {
                            let mut found = false;
                            let mut code_result = None;

                            while let Ok(Some(item)) = stream.try_next().await {
                                if let tiberius::QueryItem::Row(row) = item {
                                    found = true;
                                    let code = Code::renovation(&row);
                                    info!(
                                        "[聚合] |新建包装| 查询到箱码信息: code_flag={}, level_code={}",
                                        code.code_flag, code.level_code
                                    );
                                    code_result = Some(code);
                                    break;
                                }
                            }

                            if found {
                                code_result.unwrap()
                            } else {
                                error!("[聚合] |新建包装| 未查询到箱码信息: {}", box_code);
                                return track()
                                    .defeat()
                                    .message(format!("未查询到箱码信息: {}", box_code))
                                    .build();
                            }
                        }
                        Err(e) => {
                            error!("[聚合] |新建包装| 查询箱码失败: {}", e);
                            return track()
                                .defeat()
                                .message(format!("查询箱码失败: {}", e))
                                .build();
                        }
                    };

                    // 验证箱码条件
                    // 验证箱码状态是否为关联状态
                    if box_row.code_flag != 2 {
                        error!("[聚合] |新建包装| 箱码状态异常: {}", box_row.code_flag);
                        return track()
                            .defeat()
                            .message(format!("箱码{}状态异常，不是关联状态", box_code))
                            .build();
                    }

                    // 验证箱码层级是否为3（箱码）
                    if box_row.level_code != 3 {
                        error!(
                            "[聚合] |新建包装| 不是箱码: level_code={}",
                            box_row.level_code
                        );
                        return track()
                            .defeat()
                            .message(format!("{}不是箱码，无法执行新建包装操作", box_code))
                            .build();
                    }

                    // 验证箱码parentCode和palletCode为空
                    let box_parent_empty =
                        box_row.parent_code.as_ref().map_or(true, |v| v.is_empty());
                    let box_pallet_empty =
                        box_row.pallet_code.as_ref().map_or(true, |v| v.is_empty());

                    if !box_parent_empty || !box_pallet_empty {
                        error!(
                            "[聚合] |新建包装| 箱码不符合条件: parentCode={}, palletCode={}",
                            box_row.parent_code.as_ref().unwrap_or(&"NULL".to_string()),
                            box_row.pallet_code.as_ref().unwrap_or(&"NULL".to_string()),
                        );
                        return track()
                            .defeat()
                            .message(format!("箱码{}不符合新建包装条件", box_code))
                            .build();
                    }

                    // 如果是第一个箱码，记录批次号和产品编码
                    if index == 0 {
                        batch_no = box_row.batch_no.clone();
                        product_code = box_row.product_code.clone();
                    } else {
                        // 验证箱码批次一致
                        if box_row.batch_no != batch_no {
                            error!(
                                "[聚合] |新建包装| 箱码批次不一致: current_batch={}, first_batch={}",
                                box_row.batch_no, batch_no
                            );
                            return track()
                                .defeat()
                                .message(format!("箱码{}批次与其他箱码批次不一致", box_code))
                                .build();
                        }
                    }

                    // 箱码验证通过，添加到处理列表
                    processed_boxes.push(box_row);
                }

                // 验证箱码数量是否超过限制
                if let Err(e) = IndustryService::validate_pallet_box_count(
                    &mut conn,
                    "newPackage",
                    None,
                    &product_code,
                    params.box_codes.len(),
                )
                .await
                {
                    error!("[聚合] |新建包装| 验证箱数限制失败: {}", e);
                    return track().defeat().message(e).build();
                }

                info!("[聚合] |新建包装| 箱数限制验证通过，开始执行新建包装操作");

                // 2. 调用服务层方法执行新建包装操作
                match IndustryService::execute_new_package(
                    &mut conn,
                    &processed_boxes[0],
                    &params.box_codes,
                )
                .await
                {
                    Ok(new_pallet_code) => Ok(new_pallet_code),
                    Err(e) => Err(e),
                }
            }
            _ => {
                // 这种情况应该不会发生，因为前面已经验证了聚合类型
                Err("无效的聚合模式".to_string())
            }
        };

        // 根据处理结果提交或回滚事务
        match result {
            Ok(new_pallet_code) => {
                // 提交事务
                let commit_result = conn.simple_query("COMMIT TRANSACTION").await;
                if commit_result.is_ok() {
                    info!("[聚合] 事务提交成功，聚合操作完成");

                    // 根据聚合类型返回不同的响应数据
                    match params.aggregate_type.as_str() {
                        "putInto" => track()
                            .victory()
                            .message("放入操作成功")
                            .data(serde_json::json!({
                                "palletCode": new_pallet_code,
                                "boxCount": params.box_codes.len()
                            }))
                            .build(),
                        "newPackage" => track()
                            .victory()
                            .message("新建包装成功")
                            .data(serde_json::json!({
                                "palletCode": new_pallet_code,
                                "boxCount": params.box_codes.len()
                            }))
                            .build(),
                        _ => track().victory().message("聚合操作成功").build(),
                    }
                } else {
                    let err = commit_result.err().unwrap();
                    error!("[聚合] 提交事务失败: {}", err);

                    // 尝试回滚事务
                    let rollback_result = conn.simple_query("ROLLBACK TRANSACTION").await;
                    if rollback_result.is_ok() {
                        info!("[聚合] 事务已回滚");
                    } else {
                        error!(
                            "[聚合] 回滚事务也失败了: {}",
                            rollback_result.err().unwrap()
                        );
                    }

                    track()
                        .defeat()
                        .message(format!("提交事务失败: {}", err))
                        .build()
                }
            }
            Err(message) => {
                // 回滚事务
                let rollback_result = conn.simple_query("ROLLBACK TRANSACTION").await;
                if rollback_result.is_ok() {
                    info!("[聚合] 事务已回滚");
                } else {
                    error!("[聚合] 回滚事务失败: {}", rollback_result.err().unwrap());
                }

                track()
                    .defeat()
                    .message(format!("聚合操作失败: {}", message))
                    .build()
            }
        }
    }

    /// 取出
    pub async fn extract(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Json(params): Json<ExtractParams>,
    ) -> impl IntoResponse {
        info!("[取出] 开始取出操作流程");
        info!("[取出] 接收到请求参数: code={}", params.code);

        // 验证参数
        if params.code.is_empty() {
            error!("[取出] 参数错误: 缺少序列码");
            return track().defeat().message("请传入序列码").build();
        }

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[取出] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[取出] 数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        info!("[取出] 开始查询序列码: {}", params.code);

        // 查询序列码状态的SQL
        let sql = r#"
            SELECT id, dataID, poNumber, productCode, orderId, batchNo, levelCode, levelName, 
                   resCode, snCode, code, amount, parentCode, boxCode, palletCode, typeFlag, reqID, 
                   codeFlag, boxFlag, estate, CONVERT(varchar(19), createTime, 120) as createTime, 
                   CONVERT(varchar(19), updateTime, 120) as updateTime, fileID, helperCode, zeroBox, 
                   qualityLevel, field1, field2, boxSn
            FROM transit_code WITH (NOLOCK)
            WHERE code = @P1
        "#;

        // 查询序列码信息
        let code_row = match conn.query(sql, &[&params.code]).await {
            Ok(mut stream) => {
                let mut found = false;
                let mut code_result = None;

                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        found = true;
                        // 使用Code的renovation方法处理字段
                        let code = Code::renovation(&row);
                        info!(
                            "[取出] 查询到序列码信息: code_flag={}, level_code={}, zero_box={}, batch_no={}",
                            code.code_flag, code.level_code, code.zero_box, code.batch_no
                        );
                        code_result = Some(code);
                        break;
                    }
                }

                if found {
                    code_result.unwrap()
                } else {
                    error!("[取出] 未查询到序列码信息");
                    return track().defeat().message("未查询到序列码信息").build();
                }
            }
            Err(e) => {
                error!("[取出] 查询序列码失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("查询序列码失败: {}", e))
                    .build();
            }
        };

        // 判断序列码状态是否为关联状态（codeFlag=2表示关联状态）
        if code_row.code_flag != 2 {
            error!("[取出] 序列码状态异常: code_flag={}", code_row.code_flag);
            return track()
                .defeat()
                .message("序列码状态异常，不是关联状态，无法取出")
                .build();
        }
        info!("[取出] 序列码状态正常: code_flag={}", code_row.code_flag);

        // 开启事务
        if let Err(e) = conn.simple_query("BEGIN TRANSACTION").await {
            error!("[取出] 开启事务失败: {}", e);
            return track()
                .defeat()
                .message(format!("开启事务失败: {}", e))
                .build();
        }

        // 处理结果
        let result = match code_row.level_code {
            // 瓶码处理
            1 => {
                info!("[取出] 开始判断瓶码是否符合取出条件");

                // 检查瓶码是否有关联的托盘码
                let has_pallet = code_row
                    .pallet_code
                    .as_ref()
                    .map_or(false, |p| !p.is_empty());

                if has_pallet {
                    error!("[取出] 请先扫描所属大箱码，解除箱码与托盘的关联关系");
                    Err("请先扫描所属大箱码，解除箱码与托盘的关联关系".to_string())
                } else {
                    // 检查瓶码是否有关联的箱码和父级码
                    let has_parent = code_row
                        .parent_code
                        .as_ref()
                        .map_or(false, |p| !p.is_empty());
                    let has_box = code_row.box_code.as_ref().map_or(false, |b| !b.is_empty());
                    let parent_eq_box = if has_parent && has_box {
                        code_row.parent_code.as_ref().unwrap()
                            == code_row.box_code.as_ref().unwrap()
                    } else {
                        false
                    };

                    if has_parent && has_box && parent_eq_box {
                        info!("[取出] 瓶码已关联大箱，开始执行取出操作");

                        // 获取大箱码
                        let box_code = code_row.box_code.as_ref().unwrap().clone();

                        // 调用服务层方法取出瓶码
                        IndustryService::extract_bottle_from_box(&mut conn, &code_row, &box_code)
                            .await
                    } else {
                        error!(
                            "[取出] 瓶码不符合取出条件: parentCode={:?}, boxCode={:?}",
                            code_row.parent_code, code_row.box_code
                        );
                        Err("瓶码不符合取出条件".to_string())
                    }
                }
            }
            // 箱码处理
            3 => {
                info!("[取出] 开始判断箱码是否符合取出条件");

                // 箱码取出条件：parentCode和palletCode不为空且相等
                let parent_code = code_row.parent_code.as_ref();
                let pallet_code = code_row.pallet_code.as_ref();

                // 检查是否同时存在parentCode和palletCode，且它们相等
                if let (Some(parent), Some(pallet)) = (parent_code, pallet_code) {
                    if !parent.is_empty() && !pallet.is_empty() && parent == pallet {
                        info!("[取出] 大箱关联的托盘码: {}", pallet);

                        // 调用服务层方法取出箱码
                        IndustryService::extract_box_from_pallet(&mut conn, &code_row, pallet).await
                    } else {
                        error!(
                            "[取出] 大箱不符合取出条件: parentCode={}, palletCode={}",
                            parent, pallet
                        );
                        Err("大箱不符合取出条件".to_string())
                    }
                } else {
                    error!(
                        "[取出] 大箱不符合取出条件: parentCode={:?}, palletCode={:?}",
                        parent_code, pallet_code
                    );
                    Err("大箱不符合取出条件".to_string())
                }
            }
            // 其他层级码
            _ => {
                error!("[取出] 不支持的层级码: {}", code_row.level_code);
                Err(format!("不支持的层级码: {}", code_row.level_code))
            }
        };

        // 根据处理结果提交或回滚事务
        match result {
            Ok(_) => {
                // 提交事务
                let commit_result = conn.simple_query("COMMIT TRANSACTION").await;
                if commit_result.is_ok() {
                    info!("[取出] 事务提交成功，取出操作完成");
                    track()
                        .victory()
                        .message("序列码取出成功")
                        .data(code_row.to_response())
                        .build()
                } else {
                    let err = commit_result.err().unwrap();
                    error!("[取出] 提交事务失败: {}", err);

                    // 尝试回滚事务
                    let rollback_result = conn.simple_query("ROLLBACK TRANSACTION").await;
                    if rollback_result.is_ok() {
                        info!("[取出] 事务已回滚");
                    } else {
                        error!(
                            "[取出] 回滚事务也失败了: {}",
                            rollback_result.err().unwrap()
                        );
                    }

                    track()
                        .defeat()
                        .message(format!("提交事务失败: {}", err))
                        .build()
                }
            }
            Err(message) => {
                // 回滚事务
                let rollback_result = conn.simple_query("ROLLBACK TRANSACTION").await;
                if rollback_result.is_ok() {
                    info!("[取出] 事务已回滚");
                } else {
                    error!("[取出] 回滚事务失败: {}", rollback_result.err().unwrap());
                }

                track()
                    .defeat()
                    .message(format!("序列码取出失败: {}", message))
                    .build()
            }
        }
    }

    /// 替换
    pub async fn replace(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Json(params): Json<ReplaceParams>,
    ) -> impl IntoResponse {
        info!("[替换] 开始替换操作流程");
        info!(
            "[替换] 接收到参数: origin_code={}, replace_code={}",
            params.origin_code, params.replace_code
        );

        // 验证参数
        if params.origin_code.is_empty() || params.replace_code.is_empty() {
            error!("[替换] 参数错误: 缺少原始序列码或替换序列码");
            return track()
                .defeat()
                .message("请传入原始序列码和替换序列码")
                .build();
        }

        // 判断原始序列码和替换序列码是否相同
        if params.origin_code == params.replace_code {
            error!("[替换] 参数错误: 原始序列码和替换序列码相同");
            return track()
                .defeat()
                .message("原始序列码和替换序列码不能相同")
                .build();
        }

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[替换] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[替换] 数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        info!("[替换] 开始查询原始序列码: {}", params.origin_code);

        // 查询序列码状态的SQL
        let sql = r#"
            SELECT id, dataID, poNumber, productCode, orderId, batchNo, levelCode, levelName, 
                   resCode, snCode, code, amount, parentCode, boxCode, palletCode, typeFlag, reqID, 
                   codeFlag, boxFlag, estate, CONVERT(varchar(19), createTime, 120) as createTime, 
                   CONVERT(varchar(19), updateTime, 120) as updateTime, fileID, helperCode, zeroBox, 
                   qualityLevel, field1, field2, boxSn
            FROM transit_code WITH (NOLOCK)
            WHERE code = @P1
        "#;

        // 查询原始序列码信息
        let origin_row = match conn.query(sql, &[&params.origin_code]).await {
            Ok(mut stream) => {
                info!("[替换] 原始序列码SQL查询执行成功，开始解析结果");
                let mut found = false;
                let mut code_result = None;

                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        found = true;
                        // 使用Code的renovation方法处理字段
                        let code = Code::renovation(&row);
                        info!(
                            "[替换] 找到原始序列码信息: code_flag={}, batch_no={}",
                            code.code_flag, code.batch_no
                        );
                        code_result = Some(code);
                        break;
                    }
                }

                if found {
                    code_result.unwrap()
                } else {
                    error!("[替换] 未找到原始序列码信息");
                    return track().defeat().message("未找到原始序列码信息").build();
                }
            }
            Err(e) => {
                error!("[替换] 查询原始序列码失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("查询原始序列码失败: {}", e))
                    .build();
            }
        };

        // 判断原始序列码状态是否为关联状态（codeFlag=2表示关联状态）
        if origin_row.code_flag != 2 {
            error!("[替换] 原始序列码状态异常: {}", origin_row.code_flag);
            return track()
                .defeat()
                .message("原始序列码状态异常，不是关联状态，无法替换")
                .build();
        }
        info!(
            "[替换] 原始序列码状态正常: code_flag={}",
            origin_row.code_flag
        );

        // 查询替换序列码信息
        info!("[替换] 开始查询替换序列码: {}", params.replace_code);
        let replace_row = match conn.query(sql, &[&params.replace_code]).await {
            Ok(mut stream) => {
                info!("[替换] 替换序列码SQL查询执行成功，开始解析结果");
                let mut found = false;
                let mut code_result = None;

                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        found = true;
                        // 使用Code的renovation方法处理字段
                        let code = Code::renovation(&row);
                        info!(
                            "[替换] 找到替换序列码信息: code_flag={}, batch_no={}",
                            code.code_flag, code.batch_no
                        );
                        code_result = Some(code);
                        break;
                    }
                }

                if found {
                    code_result.unwrap()
                } else {
                    error!("[替换] 未找到替换序列码信息");
                    return track().defeat().message("未找到替换序列码信息").build();
                }
            }
            Err(e) => {
                error!("[替换] 查询替换序列码失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("查询替换序列码失败: {}", e))
                    .build();
            }
        };

        // 判断替换序列码状态是否为初始或读取状态 (codeFlag=0或1)
        if replace_row.code_flag != 0 && replace_row.code_flag != 1 {
            error!("[替换] 替换序列码状态异常: {}", replace_row.code_flag);
            return track()
                .defeat()
                .message("替换序列码状态异常，不是读取或初始状态，无法替换")
                .build();
        }
        info!(
            "[替换] 替换序列码状态正常: code_flag={}",
            replace_row.code_flag
        );

        // 判断两个序列码的层级信息是否一致
        if origin_row.level_code != replace_row.level_code
            || origin_row.batch_no != replace_row.batch_no
            || origin_row.type_flag != replace_row.type_flag
            || origin_row.box_flag != replace_row.box_flag
            || origin_row.zero_box != replace_row.zero_box
        {
            error!("[替换] 序列码信息不一致");
            return track()
                .defeat()
                .message("原始序列码和替换序列码的信息不一致，无法替换")
                .build();
        }
        info!("[替换] 序列码信息一致，可以进行替换");

        // 开始替换操作，开启事务
        info!("[替换] 开始执行替换操作，开启事务");

        // 开启事务
        if let Err(e) = conn.simple_query("BEGIN TRANSACTION").await {
            error!("[替换] 开启事务失败: {}", e);
            return track()
                .defeat()
                .message(format!("开启事务失败: {}", e))
                .build();
        }

        // 调用服务层方法执行替换操作
        let result = IndustryService::replace_code(
            &mut conn,
            &origin_row,
            &replace_row,
            &params.origin_code,
            &params.replace_code,
        )
        .await;

        // 根据处理结果提交或回滚事务
        match result {
            Ok(_) => {
                // 提交事务
                let commit_result = conn.simple_query("COMMIT TRANSACTION").await;
                if commit_result.is_ok() {
                    info!("[替换] 事务提交成功，替换操作完成");

                    // 返回成功响应
                    let response = serde_json::json!({
                        "origin_code": origin_row.to_response(),
                        "replace_code": replace_row.to_response()
                    });

                    track()
                        .victory()
                        .message("序列码替换成功")
                        .data(response)
                        .build()
                } else {
                    let err = commit_result.err().unwrap();
                    error!("[替换] 提交事务失败: {}", err);

                    // 尝试回滚事务
                    let rollback_result = conn.simple_query("ROLLBACK TRANSACTION").await;
                    if rollback_result.is_ok() {
                        info!("[替换] 事务已回滚");
                    } else {
                        error!(
                            "[替换] 回滚事务也失败了: {}",
                            rollback_result.err().unwrap()
                        );
                    }

                    track()
                        .defeat()
                        .message(format!("提交事务失败: {}", err))
                        .build()
                }
            }
            Err(message) => {
                // 回滚事务
                let rollback_result = conn.simple_query("ROLLBACK TRANSACTION").await;
                if rollback_result.is_ok() {
                    info!("[替换] 事务已回滚");
                } else {
                    error!("[替换] 回滚事务失败: {}", rollback_result.err().unwrap());
                }

                track()
                    .defeat()
                    .message(format!("序列码替换失败: {}", message))
                    .build()
            }
        }
    }

    /// 重打印
    pub async fn reprint(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Json(params): Json<ReprintParams>,
    ) -> impl IntoResponse {
        info!("[重打印] 开始重打印操作流程");
        info!("[重打印] 接收到参数: code={}", params.code);

        // 验证参数
        if params.code.is_empty() {
            error!("[重打印] 参数错误: 缺少序列码");
            return track().defeat().message("请传入序列码").build();
        }

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[重打印] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[重打印] 获取数据库连接失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 使用打印服务获取打印数据
        match PrintService::get_print_data_by_code(&mut *conn, &params.code).await {
            Ok(print_data) => {
                info!("[重打印] 获取打印数据成功");

                // 执行打印操作
                match PrintService::execute_print(&print_data).await {
                    Ok(response) => {
                        info!("[重打印] 打印成功: {}", response);
                        track().victory().message("重打印成功").build()
                    }
                    Err(e) => {
                        error!("[重打印] 打印失败: {}", e);
                        track().defeat().message(format!("打印失败: {}", e)).build()
                    }
                }
            }
            Err(e) => {
                error!("[重打印] 获取打印数据失败: {}", e);
                track()
                    .defeat()
                    .message(format!("获取打印数据失败: {}", e))
                    .build()
            }
        }
    }
}
