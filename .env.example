# 应用配置
APP_NAME=RustTravel
APP_HOST=127.0.0.1
APP_PORT=3000
APP_DEBUG=true

# 数据库配置
DB_CONNECTION=sqlsrv
DB_HOST=127.0.0.1
DB_PORT=1433
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password
DB_POOL_MAX_SIZE=10
DB_POOL_MIN_IDLE=2
DB_POOL_TIMEOUT=5000
DB_POOL_WARMUP=5
DB_POOL_MONITOR_INTERVAL=30000
DB_POOL_HEALTH_CHECK_TIMEOUT=3000

# 日志配置
LOG_LEVEL=info
LOG_PATH=./storage/logs
LOG_MAX_SIZE=10
LOG_MAX_AGE=30
LOG_MAX_BACKUPS=10
LOG_COMPRESS=true

# Socket配置
SOCKET_ENABLED=false
SOCKET_HOST=127.0.0.1
SOCKET_PORT=8080
SOCKET_CONNECT_TIMEOUT=5000
SOCKET_READ_TIMEOUT=10000
SOCKET_WRITE_TIMEOUT=5000
SOCKET_OPERATION_TIMEOUT=30000
SOCKET_RECONNECT_INTERVAL=3000
SOCKET_MAX_RECONNECT_ATTEMPTS=10
SOCKET_HEARTBEAT_INTERVAL=30000
SOCKET_READ_BUFFER_SIZE=8192
SOCKET_WRITE_BUFFER_SIZE=8192
