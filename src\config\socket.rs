use serde::Deserialize;
use std::env;

/// Socket配置结构体
#[derive(Debug, <PERSON><PERSON>, Deserialize)]
pub struct SocketConfig {
    /// 是否启用Socket连接池
    pub enabled: bool,
    /// Socket服务器地址
    pub host: String,
    /// Socket服务器端口
    pub port: u16,
    /// 连接超时时间（毫秒）
    pub connect_timeout: u64,
    /// 读取超时时间（毫秒）
    pub read_timeout: u64,
    /// 写入超时时间（毫秒）
    pub write_timeout: u64,
    /// 总操作超时时间（毫秒）
    pub operation_timeout: u64,
    /// 重连间隔（毫秒）
    pub reconnect_interval: u64,
    /// 最大重连次数
    pub max_reconnect_attempts: u32,
    /// 心跳间隔（毫秒）
    pub heartbeat_interval: u64,
    /// 读取缓冲区大小
    pub read_buffer_size: usize,
    /// 写入缓冲区大小
    pub write_buffer_size: usize,
}

impl SocketConfig {
    /// 从环境变量加载Socket配置
    pub fn from_env() -> Self {
        Self {
            enabled: env::var("SOCKET_ENABLED")
                .unwrap_or_else(|_| "false".to_string())
                .parse()
                .unwrap_or(false),
            host: env::var("SOCKET_HOST").unwrap_or_else(|_| "127.0.0.1".to_string()),
            port: env::var("SOCKET_PORT")
                .unwrap_or_else(|_| "8080".to_string())
                .parse()
                .unwrap_or(8080),
            connect_timeout: env::var("SOCKET_CONNECT_TIMEOUT")
                .unwrap_or_else(|_| "5000".to_string())
                .parse()
                .unwrap_or(5000),
            read_timeout: env::var("SOCKET_READ_TIMEOUT")
                .unwrap_or_else(|_| "10000".to_string())
                .parse()
                .unwrap_or(10000),
            write_timeout: env::var("SOCKET_WRITE_TIMEOUT")
                .unwrap_or_else(|_| "5000".to_string())
                .parse()
                .unwrap_or(5000),
            operation_timeout: env::var("SOCKET_OPERATION_TIMEOUT")
                .unwrap_or_else(|_| "30000".to_string())
                .parse()
                .unwrap_or(30000),
            reconnect_interval: env::var("SOCKET_RECONNECT_INTERVAL")
                .unwrap_or_else(|_| "3000".to_string())
                .parse()
                .unwrap_or(3000),
            max_reconnect_attempts: env::var("SOCKET_MAX_RECONNECT_ATTEMPTS")
                .unwrap_or_else(|_| "10".to_string())
                .parse()
                .unwrap_or(10),
            heartbeat_interval: env::var("SOCKET_HEARTBEAT_INTERVAL")
                .unwrap_or_else(|_| "30000".to_string())
                .parse()
                .unwrap_or(30000),
            read_buffer_size: env::var("SOCKET_READ_BUFFER_SIZE")
                .unwrap_or_else(|_| "8192".to_string())
                .parse()
                .unwrap_or(8192),
            write_buffer_size: env::var("SOCKET_WRITE_BUFFER_SIZE")
                .unwrap_or_else(|_| "8192".to_string())
                .parse()
                .unwrap_or(8192),
        }
    }

    /// 获取Socket连接字符串
    pub fn connection_string(&self) -> String {
        format!("{}:{}", self.host, self.port)
    }

    /// 获取连接超时（秒）
    pub fn connect_timeout_seconds(&self) -> f64 {
        self.connect_timeout as f64 / 1000.0
    }

    /// 获取读取超时（秒）
    pub fn read_timeout_seconds(&self) -> f64 {
        self.read_timeout as f64 / 1000.0
    }

    /// 获取写入超时（秒）
    pub fn write_timeout_seconds(&self) -> f64 {
        self.write_timeout as f64 / 1000.0
    }

    /// 获取总操作超时（秒）
    pub fn operation_timeout_seconds(&self) -> f64 {
        self.operation_timeout as f64 / 1000.0
    }

    /// 从秒设置连接超时
    pub fn set_connect_timeout_seconds(&mut self, seconds: f64) {
        self.connect_timeout = (seconds * 1000.0) as u64;
    }

    /// 从秒设置读取超时
    pub fn set_read_timeout_seconds(&mut self, seconds: f64) {
        self.read_timeout = (seconds * 1000.0) as u64;
    }

    /// 从秒设置写入超时
    pub fn set_write_timeout_seconds(&mut self, seconds: f64) {
        self.write_timeout = (seconds * 1000.0) as u64;
    }

    /// 从秒设置总操作超时
    pub fn set_operation_timeout_seconds(&mut self, seconds: f64) {
        self.operation_timeout = (seconds * 1000.0) as u64;
    }

    /// 创建用于快速网络的配置
    pub fn fast_network() -> Self {
        Self {
            enabled: true,
            host: "127.0.0.1".to_string(),
            port: 8080,
            connect_timeout: 1000,    // 1秒
            read_timeout: 2000,       // 2秒
            write_timeout: 1000,      // 1秒
            operation_timeout: 5000,  // 5秒
            reconnect_interval: 1000, // 1秒
            max_reconnect_attempts: 5,
            heartbeat_interval: 10000, // 10秒
            read_buffer_size: 4096,
            write_buffer_size: 4096,
        }
    }

    /// 创建用于慢速网络的配置
    pub fn slow_network() -> Self {
        Self {
            enabled: true,
            host: "127.0.0.1".to_string(),
            port: 8080,
            connect_timeout: 10000,   // 10秒
            read_timeout: 30000,      // 30秒
            write_timeout: 10000,     // 10秒
            operation_timeout: 60000, // 60秒
            reconnect_interval: 5000, // 5秒
            max_reconnect_attempts: 10,
            heartbeat_interval: 60000, // 60秒
            read_buffer_size: 16384,
            write_buffer_size: 16384,
        }
    }
}
